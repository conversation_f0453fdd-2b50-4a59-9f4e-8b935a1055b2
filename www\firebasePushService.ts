// Firebase Push Notification Service for iOS and Android
// Requires cordova-plugin-firebasex

export class FirebasePushService {
    static registerForPushNotifications(onRegister: (token: string) => void, onError?: (error: any) => void) {
        if (window && window['FirebasePlugin']) {
            window['FirebasePlugin'].onTokenRefresh((token: string) => {
                onRegister(token);
            }, (error: any) => {
                if (onError) onError(error);
            });
            window['FirebasePlugin'].getToken((token: string) => {
                onRegister(token);
            }, (error: any) => {
                if (onError) onError(error);
            });
        } else {
            if (onError) onError('FirebasePlugin not available');
        }
    }

    static onNotificationReceived(onMessage: (data: any) => void, onError?: (error: any) => void) {
        if (window && window['FirebasePlugin']) {
            window['FirebasePlugin'].onMessageReceived((data: any) => {
                // Parse and handle the push notification payload
                onMessage(FirebasePushService.parseNotification(data));
            }, (error: any) => {
                if (onError) onError(error);
            });
        } else {
            if (onError) onError('FirebasePlugin not available');
        }
    }

    static parseNotification(data: any): any {
        // Customize parsing logic as needed
        return {
            title: data.title || '',
            body: data.body || '',
            data: data.data || {},
            foreground: data.foreground || false,
            tap: data.tap || false
        };
    }
}

export default FirebasePushService;
