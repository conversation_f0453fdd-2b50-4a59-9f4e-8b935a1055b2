{"name": "cordova-plugin-unvired-universal-sdk", "version": "1.1.1", "description": "Un<PERSON>ed Cordova Plugin", "scripts": {"build": "webpack -c webpack.config.js", "dev": "webpack -c webpack.config.js --watch", "test": "jest"}, "cordova": {"id": "cordova-plugin-unvired-universal-sdk", "platforms": ["electron"]}, "keywords": ["ecosystem:cordova", "cordova-electron"], "author": "Unvired Inc.", "license": "UNLICENSED", "contributors": [{"name": "Venkadesh P", "email": "<EMAIL>", "url": "http://unvired.com"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://unvired.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://unvired.com"}, {"name": "Mallikarjuna G S", "email": "<EMAIL>", "url": "http://unvired.com"}], "devDependencies": {"@babel/preset-env": "^7.24.4", "@types/node": "^20.12.2", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^12.0.2", "generate-package-json-webpack-plugin": "^2.6.0", "remove-files-webpack-plugin": "^1.5.0", "terser-webpack-plugin": "^5.3.10", "ts-jest": "^29.1.2", "ts-loader": "^9.5.1", "typescript": "^5.4.3", "webpack": "^5.91.0", "webpack-cli": "^5.1.4", "webpack-shell-plugin-next": "^2.3.1"}, "dependencies": {"@babel/preset-typescript": "^7.24.1", "child_process": "^1.0.2", "crypto-js": "^4.2.0", "hi-base32": "^0.5.1", "os-browserify": "^0.3.0", "require-fool-webpack": "^3.0.0", "rxjs": "^7.8.1", "uuid": "^9.0.1"}}